cmake_minimum_required(VERSION 3.12)
project(mongoose_cpp VERSION 1.0.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set C standard for mongoose
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")
endif()

# Platform-specific settings
if(WIN32)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    set(PLATFORM_LIBS ws2_32)
elseif(UNIX)
    set(PLATFORM_LIBS pthread)
    if(NOT APPLE)
        set(PLATFORM_LIBS ${PLATFORM_LIBS} rt)
    endif()
endif()

# Mongoose configuration
add_definitions(-DMG_ENABLE_LINES=1)
add_definitions(-DMG_ENABLE_IPV6=1)
add_definitions(-DMG_ENABLE_LOG=1)

# Create mongoose library
add_library(mongoose STATIC
    mongoose.c
    mongoose.h
)

target_compile_definitions(mongoose PRIVATE
    MG_ENABLE_LINES=1
    MG_ENABLE_IPV6=1
    MG_ENABLE_LOG=1
)

if(WIN32)
    target_link_libraries(mongoose ${PLATFORM_LIBS})
endif()

# Create mongoose_cpp library
add_library(mongoose_cpp STATIC
    mongoose_cpp.hpp
    mongoose_cpp.cpp
)

target_link_libraries(mongoose_cpp 
    PUBLIC mongoose
    PRIVATE ${PLATFORM_LIBS}
)

target_include_directories(mongoose_cpp PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Example executable (commented out - no example.cpp file)
# add_executable(example
#     example.cpp
# )
#
# target_link_libraries(example
#     mongoose_cpp
#     ${PLATFORM_LIBS}
# )

# Create www directory for static files
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/www)

# Copy example HTML files
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/www/index.html
    ${CMAKE_CURRENT_BINARY_DIR}/www/index.html
    COPYONLY
)

# Install targets
install(TARGETS mongoose_cpp mongoose
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES mongoose_cpp.hpp mongoose.h
    DESTINATION include
)

# Package configuration (simplified)
include(CMakePackageConfigHelpers)

# Create a simple config file
file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/mongoose_cpp-config.cmake
"# Mongoose C++ Config
include(CMakeFindDependencyMacro)

if(NOT TARGET mongoose_cpp)
    include(\${CMAKE_CURRENT_LIST_DIR}/mongoose_cpp-targets.cmake)
endif()
")

write_basic_package_version_file(
    ${CMAKE_CURRENT_BINARY_DIR}/mongoose_cpp-config-version.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/mongoose_cpp-config.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/mongoose_cpp-config-version.cmake
    DESTINATION lib/cmake/mongoose_cpp
)

# Testing (optional)
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    enable_testing()

    # Simple test executable (commented out - no test_mongoose_cpp.cpp file)
    # add_executable(test_mongoose_cpp
    #     test_mongoose_cpp.cpp
    # )
    #
    # target_link_libraries(test_mongoose_cpp
    #     mongoose_cpp
    #     ${PLATFORM_LIBS}
    # )
    #
    # add_test(NAME mongoose_cpp_test COMMAND test_mongoose_cpp)
endif()

# Additional test executables
add_executable(route_test
    route_test.cpp
)

target_link_libraries(route_test
    mongoose_cpp
    ${PLATFORM_LIBS}
)

# Comparison test between wrapped and unwrapped mongoose
add_executable(mongoose_comparison_test
    mongoose_comparison_test.cpp
)

target_link_libraries(mongoose_comparison_test
    mongoose_cpp
    ${PLATFORM_LIBS}
)

# Simple comparison test
add_executable(simple_mongoose_test
    simple_mongoose_test.cpp
)

target_link_libraries(simple_mongoose_test
    mongoose_cpp
    ${PLATFORM_LIBS}
)

# Functional comparison test
add_executable(functional_comparison_test
    functional_comparison_test.cpp
)

target_link_libraries(functional_comparison_test
    mongoose_cpp
    ${PLATFORM_LIBS}
)

# Documentation (optional)
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        configure_file(
            ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in
            ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
            @ONLY
        )
        
        add_custom_target(docs
            ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "Mongoose C++ Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Tests: ${BUILD_TESTS}")
message(STATUS "  Build Docs: ${BUILD_DOCS}")
message(STATUS "")
