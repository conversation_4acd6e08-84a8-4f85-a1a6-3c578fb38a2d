# Mongoose C++ Wrapper

A modern C++11 wrapper for the Mongoose embedded web server library, providing RAII-style resource management, type safety, and an intuitive API for building HTTP servers, WebSocket servers, MQTT clients, and more.

## Features

- **RAII Design**: Automatic resource management with smart pointers
- **C++11 Compatible**: Uses modern C++ features like lambdas, auto, and move semantics
- **Type Safe**: Strong typing with custom exception classes
- **Modular Architecture**: Separate modules for HTTP, WebSocket, MQTT, and file serving
- **Easy to Use**: Intuitive API with method chaining and lambda callbacks
- **Thread Safe**: Built-in thread safety for connection management
- **Template Support**: Generic programming support for extensibility

## Modules

### Core
- **Manager**: Event loop and connection management
- **Connection**: Network connection abstraction
- **Timer**: Asynchronous timer functionality
- **Buffer**: Memory management utilities

### HTTP
- **Server**: HTTP server with routing support
- **Message**: HTTP request/response parsing
- **Response**: Fluent response building API
- **Route**: Pattern-based URL routing

### WebSocket
- **Server**: WebSocket server implementation
- **Client**: WebSocket client implementation
- **Message**: WebSocket message handling

### MQTT
- **Client**: MQTT client with pub/sub support
- **Options**: MQTT connection configuration
- **Message**: MQTT message handling

### File Serving
- **FileServer**: Static file serving with directory browsing

## Quick Start

### HTTP Server Example

```cpp
#include "mongoose_cpp.hpp"
using namespace mongoose_cpp;

int main() {
    Manager manager;
    http::Server server(manager);
    
    // Setup routes
    server.get("/", [](const http::Message& req, http::Response& res) {
        res.html("<h1>Hello, World!</h1>");
    });
    
    server.get("/api/hello", [](const http::Message& req, http::Response& res) {
        std::string name = req.get_query_param("name");
        if (name.empty()) name = "World";
        res.json("{\"message\": \"Hello, " + name + "!\"}");
    });
    
    // Enable static file serving
    server.static_files("./www");
    
    // Start server
    server.listen("http://0.0.0.0:8080");
    
    // Run event loop
    manager.start_polling_thread();
    
    // Keep running
    std::this_thread::sleep_for(std::chrono::hours(1));
    
    return 0;
}
```

### WebSocket Server Example

```cpp
#include "mongoose_cpp.hpp"
using namespace mongoose_cpp;

int main() {
    Manager manager;
    websocket::Server server(manager);
    
    server.on_connect([](websocket::WebSocketConnection& conn) {
        std::cout << "Client connected: " << conn.get_id() << std::endl;
        conn.send_text("Welcome!");
    });
    
    server.on_message([&server](websocket::WebSocketConnection& conn, 
                                const websocket::Message& msg) {
        if (msg.is_text()) {
            std::string text = msg.to_string();
            if (text == "broadcast") {
                server.broadcast_text("Broadcast message!");
            } else {
                conn.send_text("Echo: " + text);
            }
        }
    });
    
    server.listen("ws://0.0.0.0:8081", "/ws");
    manager.start_polling_thread();
    
    std::this_thread::sleep_for(std::chrono::hours(1));
    return 0;
}
```

### MQTT Client Example

```cpp
#include "mongoose_cpp.hpp"
using namespace mongoose_cpp;

int main() {
    Manager manager;
    mqtt::Client client(manager);
    
    client.on_connect([]() {
        std::cout << "MQTT connected!" << std::endl;
    });
    
    client.on_message([](const mqtt::Message& msg) {
        std::cout << "Received: " << msg.topic_string() 
                  << " -> " << msg.data_string() << std::endl;
    });
    
    mqtt::Options options;
    options.client_id = "my_client";
    options.keepalive = 60;
    
    client.connect("mqtt://localhost:1883", options);
    
    // Subscribe to topic
    client.subscribe("test/topic");
    
    // Publish message
    client.publish("test/topic", "Hello MQTT!");
    
    manager.start_polling_thread();
    std::this_thread::sleep_for(std::chrono::hours(1));
    
    return 0;
}
```

## Building

### Requirements
- C++11 compatible compiler (GCC 4.8+, Clang 3.3+, MSVC 2013+)
- CMake 3.12+
- Mongoose library (included)

### Build Instructions

```bash
# Clone or download the project
git clone <repository-url>
cd mongoose_cpp

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake ..

# Build
cmake --build .

# Run example
./example

# Run tests (optional)
cmake --build . --target test
```

### CMake Integration

```cmake
find_package(mongoose_cpp REQUIRED)
target_link_libraries(your_target mongoose_cpp)
```

## API Reference

### Core Classes

#### Manager
- `Manager()`: Constructor
- `void poll(int timeout_ms = 1000)`: Poll for events
- `void start_polling_thread()`: Start background polling
- `void stop_polling_thread()`: Stop background polling
- `std::shared_ptr<Connection> listen(url, handler)`: Create listening socket
- `std::shared_ptr<Connection> connect(url, handler)`: Create client connection
- `std::shared_ptr<Timer> add_timer(interval, callback, repeat)`: Add timer

#### Connection
- `bool send(data, size)`: Send data
- `size_t printf(fmt, ...)`: Send formatted data
- `bool is_valid()`: Check if connection is valid
- `void close()`: Close connection
- `Address get_remote_address()`: Get peer address

### HTTP Classes

#### Server
- `Server& get(pattern, handler)`: Add GET route
- `Server& post(pattern, handler)`: Add POST route
- `Server& static_files(root_dir)`: Enable static file serving
- `void listen(address)`: Start listening

#### Message
- `StringView method()`: Get HTTP method
- `StringView uri()`: Get request URI
- `StringView body()`: Get request body
- `std::string get_header(name)`: Get header value
- `std::string get_query_param(name)`: Get query parameter

#### Response
- `Response& status(code)`: Set status code
- `Response& header(name, value)`: Add header
- `Response& json(data)`: Send JSON response
- `Response& html(content)`: Send HTML response
- `void send()`: Send response

## License

This project is licensed under the same terms as Mongoose library (GPL v2 or commercial license).

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For questions and support, please open an issue on the project repository.
