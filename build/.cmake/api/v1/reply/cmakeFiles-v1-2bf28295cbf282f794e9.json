{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.22.1/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.22.1/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.22.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"path": "www/index.html"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.22/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/onvif/build", "source": "/home/<USER>/onvif"}, "version": {"major": 1, "minor": 0}}