{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-05ce5d17f34ae2ded80c.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "mongoose_cpp", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "functional_comparison_test::@6890427a1f51a3e7e1df", "jsonFile": "target-functional_comparison_test-Release-6acc9ff7c228ac8759e7.json", "name": "functional_comparison_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "mongoose::@6890427a1f51a3e7e1df", "jsonFile": "target-mongoose-Release-539fa2cf80228efdc119.json", "name": "mongoose", "projectIndex": 0}, {"directoryIndex": 0, "id": "mongoose_comparison_test::@6890427a1f51a3e7e1df", "jsonFile": "target-mongoose_comparison_test-Release-938fcc84f1407a5ea470.json", "name": "mongoose_comparison_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "mongoose_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-mongoose_cpp-Release-b79c099fce99685b8085.json", "name": "mongoose_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "route_test::@6890427a1f51a3e7e1df", "jsonFile": "target-route_test-Release-3149db89235843eabc1d.json", "name": "route_test", "projectIndex": 0}, {"directoryIndex": 0, "id": "simple_mongoose_test::@6890427a1f51a3e7e1df", "jsonFile": "target-simple_mongoose_test-Release-6ab47ea3625665e5db80.json", "name": "simple_mongoose_test", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/onvif/build", "source": "/home/<USER>/onvif"}, "version": {"major": 2, "minor": 3}}