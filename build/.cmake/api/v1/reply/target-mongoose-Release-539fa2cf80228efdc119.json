{"archive": {}, "artifacts": [{"path": "libmongoose.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_compile_definitions"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 1, "file": 0, "line": 87, "parent": 0}, {"command": 2, "file": 0, "line": 41, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -O3 -DNDEBUG"}, {"fragment": "-std=gnu99"}], "defines": [{"backtrace": 3, "define": "MG_ENABLE_IPV6=1"}, {"backtrace": 3, "define": "MG_ENABLE_LINES=1"}, {"backtrace": 3, "define": "MG_ENABLE_LOG=1"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "id": "mongoose::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "name": "mongoose", "nameOnDisk": "libmongoose.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "mongoose.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mongoose.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}