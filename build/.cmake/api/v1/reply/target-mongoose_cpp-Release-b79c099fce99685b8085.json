{"archive": {}, "artifacts": [{"path": "libmongoose_cpp.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "add_definitions", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 52, "parent": 0}, {"command": 1, "file": 0, "line": 87, "parent": 0}, {"command": 2, "file": 0, "line": 57, "parent": 0}, {"command": 3, "file": 0, "line": 32, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}, {"command": 3, "file": 0, "line": 33, "parent": 0}, {"command": 4, "file": 0, "line": 62, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG"}, {"fragment": "-std=c++11"}], "defines": [{"backtrace": 4, "define": "MG_ENABLE_IPV6=1"}, {"backtrace": 5, "define": "MG_ENABLE_LINES=1"}, {"backtrace": 6, "define": "MG_ENABLE_LOG=1"}], "includes": [{"backtrace": 7, "path": "/home/<USER>/onvif"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [1]}], "dependencies": [{"backtrace": 3, "id": "mongoose::@6890427a1f51a3e7e1df"}], "id": "mongoose_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "name": "mongoose_cpp", "nameOnDisk": "libmongoose_cpp.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "path": "mongoose_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mongoose_cpp.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}