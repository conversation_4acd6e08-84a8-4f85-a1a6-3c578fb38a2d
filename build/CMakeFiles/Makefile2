# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/mongoose.dir/all
all: CMakeFiles/mongoose_cpp.dir/all
all: CMakeFiles/route_test.dir/all
all: CMakeFiles/mongoose_comparison_test.dir/all
all: CMakeFiles/simple_mongoose_test.dir/all
all: CMakeFiles/functional_comparison_test.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/mongoose.dir/clean
clean: CMakeFiles/mongoose_cpp.dir/clean
clean: CMakeFiles/route_test.dir/clean
clean: CMakeFiles/mongoose_comparison_test.dir/clean
clean: CMakeFiles/simple_mongoose_test.dir/clean
clean: CMakeFiles/functional_comparison_test.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/mongoose.dir

# All Build rule for target.
CMakeFiles/mongoose.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=3,4 "Built target mongoose"
.PHONY : CMakeFiles/mongoose.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mongoose.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/mongoose.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/mongoose.dir/rule

# Convenience name for target.
mongoose: CMakeFiles/mongoose.dir/rule
.PHONY : mongoose

# clean rule for target.
CMakeFiles/mongoose.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose.dir/build.make CMakeFiles/mongoose.dir/clean
.PHONY : CMakeFiles/mongoose.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/mongoose_cpp.dir

# All Build rule for target.
CMakeFiles/mongoose_cpp.dir/all: CMakeFiles/mongoose.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=7,8 "Built target mongoose_cpp"
.PHONY : CMakeFiles/mongoose_cpp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mongoose_cpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/mongoose_cpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/mongoose_cpp.dir/rule

# Convenience name for target.
mongoose_cpp: CMakeFiles/mongoose_cpp.dir/rule
.PHONY : mongoose_cpp

# clean rule for target.
CMakeFiles/mongoose_cpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_cpp.dir/build.make CMakeFiles/mongoose_cpp.dir/clean
.PHONY : CMakeFiles/mongoose_cpp.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/route_test.dir

# All Build rule for target.
CMakeFiles/route_test.dir/all: CMakeFiles/mongoose.dir/all
CMakeFiles/route_test.dir/all: CMakeFiles/mongoose_cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/route_test.dir/build.make CMakeFiles/route_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/route_test.dir/build.make CMakeFiles/route_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=9,10 "Built target route_test"
.PHONY : CMakeFiles/route_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/route_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/route_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/route_test.dir/rule

# Convenience name for target.
route_test: CMakeFiles/route_test.dir/rule
.PHONY : route_test

# clean rule for target.
CMakeFiles/route_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/route_test.dir/build.make CMakeFiles/route_test.dir/clean
.PHONY : CMakeFiles/route_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/mongoose_comparison_test.dir

# All Build rule for target.
CMakeFiles/mongoose_comparison_test.dir/all: CMakeFiles/mongoose.dir/all
CMakeFiles/mongoose_comparison_test.dir/all: CMakeFiles/mongoose_cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_comparison_test.dir/build.make CMakeFiles/mongoose_comparison_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_comparison_test.dir/build.make CMakeFiles/mongoose_comparison_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=5,6 "Built target mongoose_comparison_test"
.PHONY : CMakeFiles/mongoose_comparison_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mongoose_comparison_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/mongoose_comparison_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/mongoose_comparison_test.dir/rule

# Convenience name for target.
mongoose_comparison_test: CMakeFiles/mongoose_comparison_test.dir/rule
.PHONY : mongoose_comparison_test

# clean rule for target.
CMakeFiles/mongoose_comparison_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mongoose_comparison_test.dir/build.make CMakeFiles/mongoose_comparison_test.dir/clean
.PHONY : CMakeFiles/mongoose_comparison_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_mongoose_test.dir

# All Build rule for target.
CMakeFiles/simple_mongoose_test.dir/all: CMakeFiles/mongoose.dir/all
CMakeFiles/simple_mongoose_test.dir/all: CMakeFiles/mongoose_cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_mongoose_test.dir/build.make CMakeFiles/simple_mongoose_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_mongoose_test.dir/build.make CMakeFiles/simple_mongoose_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=11,12 "Built target simple_mongoose_test"
.PHONY : CMakeFiles/simple_mongoose_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_mongoose_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/simple_mongoose_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_mongoose_test.dir/rule

# Convenience name for target.
simple_mongoose_test: CMakeFiles/simple_mongoose_test.dir/rule
.PHONY : simple_mongoose_test

# clean rule for target.
CMakeFiles/simple_mongoose_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_mongoose_test.dir/build.make CMakeFiles/simple_mongoose_test.dir/clean
.PHONY : CMakeFiles/simple_mongoose_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/functional_comparison_test.dir

# All Build rule for target.
CMakeFiles/functional_comparison_test.dir/all: CMakeFiles/mongoose.dir/all
CMakeFiles/functional_comparison_test.dir/all: CMakeFiles/mongoose_cpp.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/functional_comparison_test.dir/build.make CMakeFiles/functional_comparison_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/functional_comparison_test.dir/build.make CMakeFiles/functional_comparison_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=1,2 "Built target functional_comparison_test"
.PHONY : CMakeFiles/functional_comparison_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/functional_comparison_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/functional_comparison_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/onvif/build/CMakeFiles 0
.PHONY : CMakeFiles/functional_comparison_test.dir/rule

# Convenience name for target.
functional_comparison_test: CMakeFiles/functional_comparison_test.dir/rule
.PHONY : functional_comparison_test

# clean rule for target.
CMakeFiles/functional_comparison_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/functional_comparison_test.dir/build.make CMakeFiles/functional_comparison_test.dir/clean
.PHONY : CMakeFiles/functional_comparison_test.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

