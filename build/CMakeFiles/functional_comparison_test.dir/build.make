# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

# Include any dependencies generated for this target.
include CMakeFiles/functional_comparison_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/functional_comparison_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/functional_comparison_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/functional_comparison_test.dir/flags.make

CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o: CMakeFiles/functional_comparison_test.dir/flags.make
CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o: ../functional_comparison_test.cpp
CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o: CMakeFiles/functional_comparison_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o -MF CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o.d -o CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o -c /home/<USER>/onvif/functional_comparison_test.cpp

CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/onvif/functional_comparison_test.cpp > CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.i

CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/onvif/functional_comparison_test.cpp -o CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.s

# Object files for target functional_comparison_test
functional_comparison_test_OBJECTS = \
"CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o"

# External object files for target functional_comparison_test
functional_comparison_test_EXTERNAL_OBJECTS =

functional_comparison_test: CMakeFiles/functional_comparison_test.dir/functional_comparison_test.cpp.o
functional_comparison_test: CMakeFiles/functional_comparison_test.dir/build.make
functional_comparison_test: libmongoose_cpp.a
functional_comparison_test: libmongoose.a
functional_comparison_test: CMakeFiles/functional_comparison_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable functional_comparison_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/functional_comparison_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/functional_comparison_test.dir/build: functional_comparison_test
.PHONY : CMakeFiles/functional_comparison_test.dir/build

CMakeFiles/functional_comparison_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/functional_comparison_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/functional_comparison_test.dir/clean

CMakeFiles/functional_comparison_test.dir/depend:
	cd /home/<USER>/onvif/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/onvif /home/<USER>/onvif /home/<USER>/onvif/build /home/<USER>/onvif/build /home/<USER>/onvif/build/CMakeFiles/functional_comparison_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/functional_comparison_test.dir/depend

