# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

# Include any dependencies generated for this target.
include CMakeFiles/mongoose.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mongoose.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mongoose.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mongoose.dir/flags.make

CMakeFiles/mongoose.dir/mongoose.c.o: CMakeFiles/mongoose.dir/flags.make
CMakeFiles/mongoose.dir/mongoose.c.o: ../mongoose.c
CMakeFiles/mongoose.dir/mongoose.c.o: CMakeFiles/mongoose.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/mongoose.dir/mongoose.c.o"
	/usr/bin/gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mongoose.dir/mongoose.c.o -MF CMakeFiles/mongoose.dir/mongoose.c.o.d -o CMakeFiles/mongoose.dir/mongoose.c.o -c /home/<USER>/onvif/mongoose.c

CMakeFiles/mongoose.dir/mongoose.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mongoose.dir/mongoose.c.i"
	/usr/bin/gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/onvif/mongoose.c > CMakeFiles/mongoose.dir/mongoose.c.i

CMakeFiles/mongoose.dir/mongoose.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mongoose.dir/mongoose.c.s"
	/usr/bin/gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/onvif/mongoose.c -o CMakeFiles/mongoose.dir/mongoose.c.s

# Object files for target mongoose
mongoose_OBJECTS = \
"CMakeFiles/mongoose.dir/mongoose.c.o"

# External object files for target mongoose
mongoose_EXTERNAL_OBJECTS =

libmongoose.a: CMakeFiles/mongoose.dir/mongoose.c.o
libmongoose.a: CMakeFiles/mongoose.dir/build.make
libmongoose.a: CMakeFiles/mongoose.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libmongoose.a"
	$(CMAKE_COMMAND) -P CMakeFiles/mongoose.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mongoose.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mongoose.dir/build: libmongoose.a
.PHONY : CMakeFiles/mongoose.dir/build

CMakeFiles/mongoose.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mongoose.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mongoose.dir/clean

CMakeFiles/mongoose.dir/depend:
	cd /home/<USER>/onvif/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/onvif /home/<USER>/onvif /home/<USER>/onvif/build /home/<USER>/onvif/build /home/<USER>/onvif/build/CMakeFiles/mongoose.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mongoose.dir/depend

