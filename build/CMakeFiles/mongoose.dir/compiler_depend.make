# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

CMakeFiles/mongoose.dir/mongoose.c.o: ../mongoose.c \
  /usr/include/stdc-predef.h \
  ../mongoose.h \
  /usr/include/arpa/inet.h \
  /usr/include/features.h \
  /usr/include/features-time64.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/netinet/in.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/sys/socket.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
  /usr/include/x86_64-linux-gnu/bits/socket.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/select2.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/socket_type.h \
  /usr/include/x86_64-linux-gnu/bits/sockaddr.h \
  /usr/include/x86_64-linux-gnu/asm/socket.h \
  /usr/include/asm-generic/socket.h \
  /usr/include/linux/posix_types.h \
  /usr/include/linux/stddef.h \
  /usr/include/x86_64-linux-gnu/asm/posix_types.h \
  /usr/include/x86_64-linux-gnu/asm/posix_types_64.h \
  /usr/include/asm-generic/posix_types.h \
  /usr/include/x86_64-linux-gnu/asm/bitsperlong.h \
  /usr/include/asm-generic/bitsperlong.h \
  /usr/include/x86_64-linux-gnu/asm/sockios.h \
  /usr/include/asm-generic/sockios.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h \
  /usr/include/x86_64-linux-gnu/bits/socket2.h \
  /usr/include/x86_64-linux-gnu/bits/in.h \
  /usr/include/ctype.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/dirent.h \
  /usr/include/x86_64-linux-gnu/bits/dirent.h \
  /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
  /usr/include/x86_64-linux-gnu/bits/local_lim.h \
  /usr/include/linux/limits.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min.h \
  /usr/include/x86_64-linux-gnu/bits/dirent_ext.h \
  /usr/include/errno.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/linux/errno.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/fcntl.h \
  /usr/include/x86_64-linux-gnu/bits/fcntl.h \
  /usr/include/x86_64-linux-gnu/bits/fcntl-linux.h \
  /usr/include/x86_64-linux-gnu/bits/stat.h \
  /usr/include/x86_64-linux-gnu/bits/struct_stat.h \
  /usr/include/x86_64-linux-gnu/bits/fcntl2.h \
  /usr/include/inttypes.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h \
  /usr/include/stdint.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/limits.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/syslimits.h \
  /usr/include/limits.h \
  /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
  /usr/include/netdb.h \
  /usr/include/rpc/netdb.h \
  /usr/include/x86_64-linux-gnu/bits/netdb.h \
  /usr/include/netinet/tcp.h \
  /usr/include/signal.h \
  /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
  /usr/include/x86_64-linux-gnu/bits/signum-arch.h \
  /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
  /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
  /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
  /usr/include/x86_64-linux-gnu/bits/sigaction.h \
  /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
  /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
  /usr/include/x86_64-linux-gnu/sys/ucontext.h \
  /usr/include/x86_64-linux-gnu/bits/sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/sigstksz.h \
  /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
  /usr/include/x86_64-linux-gnu/bits/sigthread.h \
  /usr/include/x86_64-linux-gnu/bits/signal_ext.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdbool.h \
  /usr/include/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/include/x86_64-linux-gnu/bits/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/stdio2.h \
  /usr/include/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/alloca.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib.h \
  /usr/include/string.h \
  /usr/include/strings.h \
  /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
  /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
  /usr/include/x86_64-linux-gnu/sys/epoll.h \
  /usr/include/x86_64-linux-gnu/bits/epoll.h \
  /usr/include/x86_64-linux-gnu/sys/stat.h \
  /usr/include/x86_64-linux-gnu/sys/time.h \
  /usr/include/time.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/unistd.h \
  /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
  /usr/include/x86_64-linux-gnu/bits/environments.h \
  /usr/include/x86_64-linux-gnu/bits/confname.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
  /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
  /usr/include/x86_64-linux-gnu/bits/unistd.h \
  /usr/include/x86_64-linux-gnu/bits/unistd_ext.h


/usr/include/x86_64-linux-gnu/bits/unistd_ext.h:

/usr/include/x86_64-linux-gnu/bits/unistd.h:

/usr/include/x86_64-linux-gnu/bits/getopt_core.h:

/usr/include/x86_64-linux-gnu/bits/getopt_posix.h:

/usr/include/x86_64-linux-gnu/bits/environments.h:

/usr/include/x86_64-linux-gnu/bits/posix_opt.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h:

/usr/include/x86_64-linux-gnu/bits/time.h:

/usr/include/time.h:

/usr/include/x86_64-linux-gnu/sys/time.h:

/usr/include/x86_64-linux-gnu/bits/string_fortified.h:

/usr/include/x86_64-linux-gnu/bits/strings_fortified.h:

/usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h:

/usr/include/alloca.h:

/usr/include/x86_64-linux-gnu/bits/waitstatus.h:

/usr/include/x86_64-linux-gnu/bits/waitflags.h:

/usr/include/x86_64-linux-gnu/bits/stdio2.h:

/usr/include/string.h:

/usr/include/x86_64-linux-gnu/bits/stdio.h:

/usr/include/x86_64-linux-gnu/bits/epoll.h:

/usr/include/x86_64-linux-gnu/bits/floatn-common.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h:

/usr/include/x86_64-linux-gnu/bits/floatn.h:

/usr/include/asm-generic/sockios.h:

/usr/include/x86_64-linux-gnu/asm/sockios.h:

/usr/include/linux/posix_types.h:

/usr/include/x86_64-linux-gnu/bits/posix1_lim.h:

/usr/include/asm-generic/bitsperlong.h:

/usr/include/inttypes.h:

/usr/include/stdlib.h:

/usr/include/x86_64-linux-gnu/asm/socket.h:

/usr/include/x86_64-linux-gnu/bits/timesize.h:

/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h:

/usr/include/x86_64-linux-gnu/asm/bitsperlong.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h:

/usr/include/strings.h:

/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h:

/usr/include/x86_64-linux-gnu/bits/types/time_t.h:

/usr/include/x86_64-linux-gnu/bits/select2.h:

/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h:

/usr/include/x86_64-linux-gnu/bits/pthread_stack_min.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h:

/usr/include/x86_64-linux-gnu/sys/cdefs.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/byteswap.h:

../mongoose.c:

/usr/include/x86_64-linux-gnu/bits/time64.h:

/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/select.h:

/usr/include/x86_64-linux-gnu/bits/struct_stat.h:

/usr/include/features-time64.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:

/usr/include/asm-generic/socket.h:

/usr/include/x86_64-linux-gnu/bits/socket2.h:

/usr/include/x86_64-linux-gnu/bits/wordsize.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h:

/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h:

/usr/include/x86_64-linux-gnu/sys/epoll.h:

/usr/include/features.h:

/usr/include/x86_64-linux-gnu/asm/posix_types.h:

/usr/include/x86_64-linux-gnu/bits/long-double.h:

/usr/include/arpa/inet.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h:

/usr/include/limits.h:

/usr/include/linux/stddef.h:

../mongoose.h:

/usr/include/x86_64-linux-gnu/bits/sockaddr.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h:

/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:

/usr/include/x86_64-linux-gnu/bits/struct_mutex.h:

/usr/include/endian.h:

/usr/include/x86_64-linux-gnu/bits/typesizes.h:

/usr/include/ctype.h:

/usr/include/x86_64-linux-gnu/sys/stat.h:

/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:

/usr/include/unistd.h:

/usr/include/x86_64-linux-gnu/sys/select.h:

/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h:

/usr/include/x86_64-linux-gnu/bits/endianness.h:

/usr/include/x86_64-linux-gnu/sys/socket.h:

/usr/include/asm-generic/posix_types.h:

/usr/include/stdc-predef.h:

/usr/include/x86_64-linux-gnu/bits/socket_type.h:

/usr/include/x86_64-linux-gnu/sys/types.h:

/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h:

/usr/include/x86_64-linux-gnu/bits/types.h:

/usr/include/x86_64-linux-gnu/bits/stdint-intn.h:

/usr/include/x86_64-linux-gnu/gnu/stubs.h:

/usr/include/x86_64-linux-gnu/bits/endian.h:

/usr/include/x86_64-linux-gnu/bits/in.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/limits.h:

/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:

/usr/include/stdio.h:

/usr/include/dirent.h:

/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:

/usr/include/x86_64-linux-gnu/bits/dirent.h:

/usr/include/x86_64-linux-gnu/bits/signal_ext.h:

/usr/include/x86_64-linux-gnu/bits/dirent_ext.h:

/usr/include/x86_64-linux-gnu/bits/errno.h:

/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h:

/usr/include/linux/errno.h:

/usr/include/x86_64-linux-gnu/bits/fcntl.h:

/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h:

/usr/include/x86_64-linux-gnu/bits/stdlib.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h:

/usr/include/x86_64-linux-gnu/asm/errno.h:

/usr/include/x86_64-linux-gnu/bits/stdlib-float.h:

/usr/include/x86_64-linux-gnu/bits/uintn-identity.h:

/usr/include/x86_64-linux-gnu/bits/ss_flags.h:

/usr/include/asm-generic/errno.h:

/usr/include/x86_64-linux-gnu/asm/posix_types_64.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/fcntl.h:

/usr/include/x86_64-linux-gnu/bits/fcntl-linux.h:

/usr/include/x86_64-linux-gnu/bits/fcntl2.h:

/usr/include/linux/limits.h:

/usr/include/x86_64-linux-gnu/bits/posix2_lim.h:

/usr/include/stdint.h:

/usr/include/rpc/netdb.h:

/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:

/usr/include/x86_64-linux-gnu/bits/local_lim.h:

/usr/include/x86_64-linux-gnu/bits/wchar.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/syslimits.h:

/usr/include/x86_64-linux-gnu/bits/types/clock_t.h:

/usr/include/netinet/in.h:

/usr/include/netdb.h:

/usr/include/x86_64-linux-gnu/bits/types/timer_t.h:

/usr/include/x86_64-linux-gnu/bits/signum-generic.h:

/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h:

/usr/include/x86_64-linux-gnu/bits/sigcontext.h:

/usr/include/x86_64-linux-gnu/bits/netdb.h:

/usr/include/netinet/tcp.h:

/usr/include/x86_64-linux-gnu/bits/sigstksz.h:

/usr/include/x86_64-linux-gnu/bits/signum-arch.h:

/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h:

/usr/include/errno.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h:

/usr/include/x86_64-linux-gnu/bits/confname.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h:

/usr/include/x86_64-linux-gnu/bits/stat.h:

/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h:

/usr/include/x86_64-linux-gnu/bits/sigaction.h:

/usr/include/x86_64-linux-gnu/bits/types/stack_t.h:

/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:

/usr/include/x86_64-linux-gnu/sys/ucontext.h:

/usr/include/x86_64-linux-gnu/bits/socket.h:

/usr/include/x86_64-linux-gnu/bits/sigstack.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h:

/usr/include/x86_64-linux-gnu/bits/sigthread.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stdbool.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:

/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:

/usr/include/signal.h:

/usr/include/x86_64-linux-gnu/bits/types/FILE.h:
