# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

# Include any dependencies generated for this target.
include CMakeFiles/mongoose_comparison_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mongoose_comparison_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mongoose_comparison_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mongoose_comparison_test.dir/flags.make

CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o: CMakeFiles/mongoose_comparison_test.dir/flags.make
CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o: ../mongoose_comparison_test.cpp
CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o: CMakeFiles/mongoose_comparison_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o -MF CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o.d -o CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o -c /home/<USER>/onvif/mongoose_comparison_test.cpp

CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/onvif/mongoose_comparison_test.cpp > CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.i

CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/onvif/mongoose_comparison_test.cpp -o CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.s

# Object files for target mongoose_comparison_test
mongoose_comparison_test_OBJECTS = \
"CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o"

# External object files for target mongoose_comparison_test
mongoose_comparison_test_EXTERNAL_OBJECTS =

mongoose_comparison_test: CMakeFiles/mongoose_comparison_test.dir/mongoose_comparison_test.cpp.o
mongoose_comparison_test: CMakeFiles/mongoose_comparison_test.dir/build.make
mongoose_comparison_test: libmongoose_cpp.a
mongoose_comparison_test: libmongoose.a
mongoose_comparison_test: CMakeFiles/mongoose_comparison_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable mongoose_comparison_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mongoose_comparison_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mongoose_comparison_test.dir/build: mongoose_comparison_test
.PHONY : CMakeFiles/mongoose_comparison_test.dir/build

CMakeFiles/mongoose_comparison_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mongoose_comparison_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mongoose_comparison_test.dir/clean

CMakeFiles/mongoose_comparison_test.dir/depend:
	cd /home/<USER>/onvif/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/onvif /home/<USER>/onvif /home/<USER>/onvif/build /home/<USER>/onvif/build /home/<USER>/onvif/build/CMakeFiles/mongoose_comparison_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mongoose_comparison_test.dir/depend

