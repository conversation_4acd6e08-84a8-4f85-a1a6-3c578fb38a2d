# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/onvif

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/onvif/build

# Include any dependencies generated for this target.
include CMakeFiles/mongoose_cpp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mongoose_cpp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mongoose_cpp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mongoose_cpp.dir/flags.make

CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o: CMakeFiles/mongoose_cpp.dir/flags.make
CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o: ../mongoose_cpp.cpp
CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o: CMakeFiles/mongoose_cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o -MF CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o.d -o CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o -c /home/<USER>/onvif/mongoose_cpp.cpp

CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/onvif/mongoose_cpp.cpp > CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.i

CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/onvif/mongoose_cpp.cpp -o CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.s

# Object files for target mongoose_cpp
mongoose_cpp_OBJECTS = \
"CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o"

# External object files for target mongoose_cpp
mongoose_cpp_EXTERNAL_OBJECTS =

libmongoose_cpp.a: CMakeFiles/mongoose_cpp.dir/mongoose_cpp.cpp.o
libmongoose_cpp.a: CMakeFiles/mongoose_cpp.dir/build.make
libmongoose_cpp.a: CMakeFiles/mongoose_cpp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/onvif/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libmongoose_cpp.a"
	$(CMAKE_COMMAND) -P CMakeFiles/mongoose_cpp.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mongoose_cpp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mongoose_cpp.dir/build: libmongoose_cpp.a
.PHONY : CMakeFiles/mongoose_cpp.dir/build

CMakeFiles/mongoose_cpp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mongoose_cpp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mongoose_cpp.dir/clean

CMakeFiles/mongoose_cpp.dir/depend:
	cd /home/<USER>/onvif/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/onvif /home/<USER>/onvif /home/<USER>/onvif/build /home/<USER>/onvif/build /home/<USER>/onvif/build/CMakeFiles/mongoose_cpp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mongoose_cpp.dir/depend

