<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mongoose C++ Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mongoose C++ Example</h1>
        
        <div class="section">
            <h3>HTTP API Test</h3>
            <button onclick="testAPI()">Test Hello API</button>
            <button onclick="testJSON()">Test JSON API</button>
            <input type="text" id="nameInput" placeholder="Enter name" value="World">
        </div>
        
        <div class="section">
            <h3>WebSocket Test</h3>
            <button onclick="connectWebSocket()">Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <button onclick="sendPing()">Send Ping</button>
            <button onclick="sendBroadcast()">Send Broadcast</button>
            <input type="text" id="wsMessage" placeholder="Enter message" value="Hello WebSocket">
            <button onclick="sendMessage()">Send Message</button>
        </div>
        
        <div class="section">
            <h3>Output</h3>
            <div id="output"></div>
            <button onclick="clearOutput()">Clear Output</button>
        </div>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        async function testAPI() {
            try {
                const name = document.getElementById('nameInput').value || 'World';
                const response = await fetch(`/api/hello?name=${encodeURIComponent(name)}`);
                const data = await response.json();
                log(`API Response: ${JSON.stringify(data)}`);
            } catch (error) {
                log(`API Error: ${error.message}`);
            }
        }
        
        async function testJSON() {
            try {
                const response = await fetch('/json');
                const data = await response.json();
                log(`JSON Response: ${JSON.stringify(data)}`);
            } catch (error) {
                log(`JSON Error: ${error.message}`);
            }
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket already connected');
                return;
            }
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.hostname}:8081/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                log('WebSocket connected');
            };
            
            ws.onmessage = function(event) {
                log(`WebSocket received: ${event.data}`);
            };
            
            ws.onclose = function(event) {
                log('WebSocket disconnected');
                ws = null;
            };
            
            ws.onerror = function(error) {
                log(`WebSocket error: ${error}`);
            };
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('WebSocket disconnected');
            } else {
                log('WebSocket not connected');
            }
        }
        
        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send('ping');
                log('Sent: ping');
            } else {
                log('WebSocket not connected');
            }
        }
        
        function sendBroadcast() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send('broadcast');
                log('Sent: broadcast');
            } else {
                log('WebSocket not connected');
            }
        }
        
        function sendMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = document.getElementById('wsMessage').value;
                ws.send(message);
                log(`Sent: ${message}`);
            } else {
                log('WebSocket not connected');
            }
        }
        
        // Auto-connect WebSocket on page load
        window.onload = function() {
            log('Page loaded - Mongoose C++ Example');
            setTimeout(connectWebSocket, 1000);
        };
    </script>
</body>
</html>
