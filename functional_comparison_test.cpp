#include "mongoose_cpp.hpp"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <atomic>

extern "C" {
#include "mongoose.h"
}

// Global variables for unwrapped version
static std::atomic<int> unwrapped_request_count{0};
static std::atomic<bool> unwrapped_server_running{false};

// Event handler for unwrapped mongoose
static void unwrapped_event_handler(struct mg_connection *c, int ev, void *ev_data) {
    if (ev == MG_EV_HTTP_MSG) {
        struct mg_http_message *hm = (struct mg_http_message *) ev_data;
        
        // Handle different routes
        if (mg_match(hm->uri, mg_str("/"), NULL)) {
            mg_http_reply(c, 200, "Content-Type: text/html\r\n", 
                         "<h1>Hello from Unwrapped Mongoose!</h1>"
                         "<p>Request count: %d</p>", 
                         unwrapped_request_count.fetch_add(1) + 1);
        } 
        else if (mg_match(hm->uri, mg_str("/api/status"), NULL)) {
            mg_http_reply(c, 200, "Content-Type: application/json\r\n", 
                         "{\"status\":\"ok\",\"requests\":%d,\"wrapped\":false}", 
                         unwrapped_request_count.load());
        }
        else if (mg_match(hm->uri, mg_str("/api/echo"), NULL)) {
            // Echo the request body
            mg_http_reply(c, 200, "Content-Type: text/plain\r\n", 
                         "%.*s", (int)hm->body.len, hm->body.buf);
        }
        else {
            mg_http_reply(c, 404, "Content-Type: text/plain\r\n", "Not Found");
        }
    }
}

class FunctionalComparisonTest {
private:
    std::atomic<int> wrapped_request_count_{0};
    static constexpr int WRAPPED_PORT = 8082;
    static constexpr int UNWRAPPED_PORT = 8083;
    
public:
    void run_tests() {
        std::cout << "=== Functional Comparison Test ===" << std::endl;
        std::cout << "Testing actual HTTP server implementations" << std::endl;
        std::cout << std::endl;
        
        // Run both servers concurrently for comparison
        std::thread wrapped_thread(&FunctionalComparisonTest::run_wrapped_server, this);
        std::thread unwrapped_thread(&FunctionalComparisonTest::run_unwrapped_server, this);
        
        // Wait for servers to start
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // Test both servers
        test_servers();
        
        // Stop servers
        std::cout << "Stopping servers..." << std::endl;
        unwrapped_server_running = false;
        
        if (wrapped_thread.joinable()) {
            wrapped_thread.join();
        }
        if (unwrapped_thread.joinable()) {
            unwrapped_thread.join();
        }
        
        print_results();
    }
    
private:
    void run_wrapped_server() {
        try {
            std::cout << "Starting Wrapped Server on port " << WRAPPED_PORT << std::endl;
            
            mongoose_cpp::Manager manager;
            mongoose_cpp::http::Server server(manager);
            
            // Setup routes with modern C++ style
            server.get("/", [this](const mongoose_cpp::http::Message& req, mongoose_cpp::http::Response& res) {
                (void)req;
                int count = wrapped_request_count_.fetch_add(1) + 1;
                res.html("<h1>Hello from Wrapped Mongoose!</h1>"
                        "<p>Request count: " + std::to_string(count) + "</p>")
                   .send();
            });
            
            server.get("/api/status", [this](const mongoose_cpp::http::Message& req, mongoose_cpp::http::Response& res) {
                (void)req;
                res.json("{\"status\":\"ok\",\"requests\":" + 
                        std::to_string(wrapped_request_count_.load()) + 
                        ",\"wrapped\":true}")
                   .send();
            });
            
            server.post("/api/echo", [](const mongoose_cpp::http::Message& req, mongoose_cpp::http::Response& res) {
                res.text(req.body().to_string()).send();
            });
            
            // Start server
            server.listen("http://0.0.0.0:" + std::to_string(WRAPPED_PORT));
            manager.start_polling_thread();
            
            std::cout << "✓ Wrapped server started successfully" << std::endl;
            
            // Keep server running
            while (unwrapped_server_running.load()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
            manager.stop_polling_thread();
            std::cout << "✓ Wrapped server stopped" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "✗ Wrapped server error: " << e.what() << std::endl;
        }
    }
    
    void run_unwrapped_server() {
        std::cout << "Starting Unwrapped Server on port " << UNWRAPPED_PORT << std::endl;
        
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        
        struct mg_connection *conn = mg_listen(&mgr, 
            ("http://0.0.0.0:" + std::to_string(UNWRAPPED_PORT)).c_str(), 
            unwrapped_event_handler, NULL);
        
        if (conn) {
            std::cout << "✓ Unwrapped server started successfully" << std::endl;
            unwrapped_server_running = true;
            
            // Event loop
            while (unwrapped_server_running.load()) {
                mg_mgr_poll(&mgr, 100);
            }
            
            std::cout << "✓ Unwrapped server stopped" << std::endl;
        } else {
            std::cerr << "✗ Failed to start unwrapped server" << std::endl;
        }
        
        mg_mgr_free(&mgr);
    }
    
    void test_servers() {
        std::cout << "\nTesting servers..." << std::endl;
        
        // Test wrapped server
        std::cout << "\nTesting Wrapped Server:" << std::endl;
        test_server_endpoints("127.0.0.1", WRAPPED_PORT, "wrapped");
        
        // Test unwrapped server  
        std::cout << "\nTesting Unwrapped Server:" << std::endl;
        test_server_endpoints("127.0.0.1", UNWRAPPED_PORT, "unwrapped");
    }
    
    void test_server_endpoints(const std::string& host, int port, const std::string& server_type) {
        // Simple HTTP client test using mongoose
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        
        std::atomic<int> responses_received{0};
        std::atomic<bool> test_complete{false};
        
        // Test GET /
        auto test_get_root = [&]() {
            struct mg_connection *conn = mg_connect(&mgr, 
                ("http://" + host + ":" + std::to_string(port) + "/").c_str(),
                [](struct mg_connection *c, int ev, void *ev_data) {
                    if (ev == MG_EV_HTTP_MSG) {
                        struct mg_http_message *hm = (struct mg_http_message *) ev_data;
                        std::cout << "  ✓ GET / response received (status: " << hm->message.buf[9] << hm->message.buf[10] << hm->message.buf[11] << ")" << std::endl;
                        static_cast<std::atomic<int>*>(c->fn_data)->fetch_add(1);
                    }
                }, &responses_received);
            
            if (conn) {
                mg_printf(conn, "GET / HTTP/1.1\r\nHost: %s\r\nConnection: close\r\n\r\n", host.c_str());
            }
        };
        
        // Test GET /api/status
        auto test_get_status = [&]() {
            struct mg_connection *conn = mg_connect(&mgr, 
                ("http://" + host + ":" + std::to_string(port) + "/api/status").c_str(),
                [](struct mg_connection *c, int ev, void *ev_data) {
                    if (ev == MG_EV_HTTP_MSG) {
                        std::cout << "  ✓ GET /api/status response received" << std::endl;
                        static_cast<std::atomic<int>*>(c->fn_data)->fetch_add(1);
                    }
                }, &responses_received);
            
            if (conn) {
                mg_printf(conn, "GET /api/status HTTP/1.1\r\nHost: %s\r\nConnection: close\r\n\r\n", host.c_str());
            }
        };
        
        // Execute tests
        test_get_root();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        test_get_status();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Poll for responses
        auto start_time = std::chrono::steady_clock::now();
        while (responses_received.load() < 2 && 
               std::chrono::steady_clock::now() - start_time < std::chrono::seconds(5)) {
            mg_mgr_poll(&mgr, 50);
        }
        
        std::cout << "  📊 " << server_type << " server: " << responses_received.load() << "/2 responses received" << std::endl;
        
        mg_mgr_free(&mgr);
    }
    
    void print_results() {
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Wrapped server handled " << wrapped_request_count_.load() << " requests" << std::endl;
        std::cout << "Unwrapped server handled " << unwrapped_request_count.load() << " requests" << std::endl;
        
        std::cout << "\n=== Code Comparison ===" << std::endl;
        
        std::cout << "\n📝 Wrapped Version Highlights:" << std::endl;
        std::cout << "  • Route definition: server.get(\"/path\", lambda)" << std::endl;
        std::cout << "  • Response building: res.html(content).send()" << std::endl;
        std::cout << "  • Automatic request counting with atomic operations" << std::endl;
        std::cout << "  • Exception-safe resource management" << std::endl;
        std::cout << "  • Type-safe HTTP status and content-type handling" << std::endl;
        
        std::cout << "\n📝 Unwrapped Version Highlights:" << std::endl;
        std::cout << "  • Route matching: mg_match(hm->uri, mg_str(\"/path\"), NULL)" << std::endl;
        std::cout << "  • Response: mg_http_reply(c, 200, headers, body)" << std::endl;
        std::cout << "  • Manual string formatting with printf-style" << std::endl;
        std::cout << "  • Manual resource management (mg_mgr_init/free)" << std::endl;
        std::cout << "  • Direct access to HTTP message structures" << std::endl;
        
        std::cout << "\n🎯 Key Differences:" << std::endl;
        std::cout << "  • Wrapped: Modern C++, RAII, type safety, convenience" << std::endl;
        std::cout << "  • Unwrapped: C-style, manual management, direct control, minimal overhead" << std::endl;
        
        std::cout << std::endl;
    }
};

int main() {
    FunctionalComparisonTest test;
    test.run_tests();
    return 0;
}
