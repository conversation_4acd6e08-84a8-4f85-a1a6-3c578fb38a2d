#include "mongoose_cpp.hpp"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <atomic>
#include <cassert>
#include <iomanip>

extern "C" {
#include "mongoose.h"
}

class MongooseComparisonTest {
private:
    std::atomic<bool> test_passed_{false};
    std::atomic<int> wrapped_responses_{0};
    std::atomic<int> unwrapped_responses_{0};
    
    // Test ports
    static constexpr int WRAPPED_PORT = 8080;
    static constexpr int UNWRAPPED_PORT = 8081;
    
public:
    void run_all_tests() {
        std::cout << "=== Mongoose C++ Wrapper vs Raw Mongoose Comparison Test ===" << std::endl;
        std::cout << std::endl;
        
        // Test 1: Basic HTTP Server Functionality
        test_http_server_comparison();
        
        // Test 2: Connection Management
        test_connection_management();
        
        // Test 3: Performance Comparison
        test_performance_comparison();
        
        // Test 4: Error Handling
        test_error_handling();
        
        // Test 5: Memory Management
        test_memory_management();
        
        std::cout << std::endl;
        std::cout << "=== All Tests Completed ===" << std::endl;
    }
    
private:
    void test_http_server_comparison() {
        std::cout << "Test 1: HTTP Server Functionality Comparison" << std::endl;
        std::cout << "--------------------------------------------" << std::endl;
        
        // Test wrapped version
        std::cout << "Testing Wrapped Mongoose (mongoose_cpp):" << std::endl;
        test_wrapped_http_server();
        
        std::cout << std::endl;
        
        // Test unwrapped version
        std::cout << "Testing Unwrapped Mongoose (raw C API):" << std::endl;
        test_unwrapped_http_server();
        
        std::cout << std::endl;
    }
    
    void test_wrapped_http_server() {
        try {
            mongoose_cpp::Manager manager;
            mongoose_cpp::http::Server server(manager);
            
            // Setup routes
            server.get("/", [this](const mongoose_cpp::http::Message& req, mongoose_cpp::http::Response& res) {
                (void)req;
                res.text("Hello from Wrapped Mongoose!")
                   .status(mongoose_cpp::http::Status::OK)
                   .send();
                wrapped_responses_++;
            });
            
            server.get("/test", [this](const mongoose_cpp::http::Message& req, mongoose_cpp::http::Response& res) {
                (void)req;
                res.json("{\"message\": \"Test endpoint\", \"wrapped\": true}")
                   .send();
                wrapped_responses_++;
            });
            
            // Start server
            server.listen("http://0.0.0.0:" + std::to_string(WRAPPED_PORT));
            std::cout << "  ✓ Wrapped server started on port " << WRAPPED_PORT << std::endl;
            
            // Start polling in background
            manager.start_polling_thread();
            
            // Wait a bit for server to be ready
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            // Test client connection
            test_wrapped_client(manager);
            
            // Stop server
            manager.stop_polling_thread();
            std::cout << "  ✓ Wrapped server stopped" << std::endl;
            std::cout << "  ✓ Wrapped responses handled: " << wrapped_responses_.load() << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "  ✗ Wrapped server error: " << e.what() << std::endl;
        }
    }
    
    void test_wrapped_client(mongoose_cpp::Manager& manager) {
        try {
            std::atomic<bool> response_received{false};
            
            auto conn = manager.connect("http://127.0.0.1:" + std::to_string(WRAPPED_PORT), 
                [&response_received](mongoose_cpp::Connection& conn, int event, void* event_data) {
                    if (event == MG_EV_HTTP_MSG) {
                        response_received = true;
                        std::cout << "  ✓ Wrapped client received HTTP response" << std::endl;
                    }
                });
            
            if (conn) {
                conn->printf("GET / HTTP/1.1\r\nHost: localhost\r\nConnection: close\r\n\r\n");
                
                // Wait for response
                auto start = std::chrono::steady_clock::now();
                while (!response_received && 
                       std::chrono::steady_clock::now() - start < std::chrono::seconds(2)) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
                
                if (response_received) {
                    std::cout << "  ✓ Wrapped client test successful" << std::endl;
                } else {
                    std::cout << "  ✗ Wrapped client timeout" << std::endl;
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "  ✗ Wrapped client error: " << e.what() << std::endl;
        }
    }
    
    void test_unwrapped_http_server() {
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        
        try {
            // Event handler for unwrapped version
            auto event_handler = [](struct mg_connection *c, int ev, void *ev_data) {
                MongooseComparisonTest* test = static_cast<MongooseComparisonTest*>(c->mgr->userdata);
                
                if (ev == MG_EV_HTTP_MSG) {
                    struct mg_http_message *hm = (struct mg_http_message *) ev_data;
                    
                    if (mg_match(hm->uri, mg_str("/"), NULL)) {
                        mg_http_reply(c, 200, "Content-Type: text/plain\r\n", 
                                     "Hello from Unwrapped Mongoose!");
                        test->unwrapped_responses_++;
                    } else if (mg_match(hm->uri, mg_str("/test"), NULL)) {
                        mg_http_reply(c, 200, "Content-Type: application/json\r\n", 
                                     "{\"message\": \"Test endpoint\", \"wrapped\": false}");
                        test->unwrapped_responses_++;
                    } else {
                        mg_http_reply(c, 404, "", "Not Found");
                    }
                }
            };
            
            mgr.userdata = this;
            
            // Start server
            struct mg_connection *conn = mg_listen(&mgr, 
                ("http://0.0.0.0:" + std::to_string(UNWRAPPED_PORT)).c_str(), 
                event_handler, NULL);
            
            if (conn) {
                std::cout << "  ✓ Unwrapped server started on port " << UNWRAPPED_PORT << std::endl;
                
                // Poll for a short time to handle requests
                auto start = std::chrono::steady_clock::now();
                while (std::chrono::steady_clock::now() - start < std::chrono::seconds(1)) {
                    mg_mgr_poll(&mgr, 10);
                }
                
                std::cout << "  ✓ Unwrapped server stopped" << std::endl;
                std::cout << "  ✓ Unwrapped responses handled: " << unwrapped_responses_.load() << std::endl;
            } else {
                std::cout << "  ✗ Failed to start unwrapped server" << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cerr << "  ✗ Unwrapped server error: " << e.what() << std::endl;
        }
        
        mg_mgr_free(&mgr);
    }
    
    void test_connection_management() {
        std::cout << "Test 2: Connection Management Comparison" << std::endl;
        std::cout << "---------------------------------------" << std::endl;
        
        // Wrapped version - RAII automatic cleanup
        std::cout << "Wrapped version (RAII):" << std::endl;
        {
            mongoose_cpp::Manager manager;
            std::cout << "  ✓ Manager created (automatic initialization)" << std::endl;
            
            auto conn = manager.connect("http://httpbin.org/get", 
                [](mongoose_cpp::Connection& conn, int event, void* event_data) {
                    (void)conn; (void)event; (void)event_data;
                });
            
            if (conn) {
                std::cout << "  ✓ Connection created with shared_ptr (automatic cleanup)" << std::endl;
                std::cout << "  ✓ Connection ID: " << conn->get_id() << std::endl;
            }
        } // Automatic cleanup here
        std::cout << "  ✓ Manager and connections automatically cleaned up" << std::endl;
        
        std::cout << std::endl;
        
        // Unwrapped version - Manual cleanup required
        std::cout << "Unwrapped version (Manual):" << std::endl;
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        std::cout << "  ✓ Manager manually initialized" << std::endl;
        
        struct mg_connection *conn = mg_connect(&mgr, "http://httpbin.org/get", 
            [](struct mg_connection *c, int ev, void *ev_data) {
                (void)c; (void)ev; (void)ev_data;
            }, NULL);
        
        if (conn) {
            std::cout << "  ✓ Connection created (manual cleanup required)" << std::endl;
            std::cout << "  ✓ Connection ID: " << conn->id << std::endl;
        }
        
        mg_mgr_free(&mgr);
        std::cout << "  ✓ Manager manually cleaned up" << std::endl;
        
        std::cout << std::endl;
    }
    
    void test_performance_comparison() {
        std::cout << "Test 3: Performance Comparison" << std::endl;
        std::cout << "------------------------------" << std::endl;
        
        const int iterations = 1000;
        
        // Test wrapped version performance
        auto start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < iterations; ++i) {
            mongoose_cpp::Manager manager;
            // Simulate some work
            manager.set_dns_timeout(1000);
        }
        auto end = std::chrono::high_resolution_clock::now();
        auto wrapped_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "  Wrapped version (" << iterations << " iterations): " 
                  << wrapped_time.count() << " μs" << std::endl;
        
        // Test unwrapped version performance
        start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < iterations; ++i) {
            struct mg_mgr mgr;
            mg_mgr_init(&mgr);
            // Simulate some work
            mgr.dnstimeout = 1000;
            mg_mgr_free(&mgr);
        }
        end = std::chrono::high_resolution_clock::now();
        auto unwrapped_time = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "  Unwrapped version (" << iterations << " iterations): " 
                  << unwrapped_time.count() << " μs" << std::endl;
        
        double overhead = (double(wrapped_time.count()) / double(unwrapped_time.count()) - 1.0) * 100.0;
        std::cout << "  Wrapper overhead: " << std::fixed << std::setprecision(2) 
                  << overhead << "%" << std::endl;
        
        std::cout << std::endl;
    }
    
    void test_error_handling() {
        std::cout << "Test 4: Error Handling Comparison" << std::endl;
        std::cout << "---------------------------------" << std::endl;
        
        // Wrapped version - Exception-based error handling
        std::cout << "Wrapped version (Exceptions):" << std::endl;
        try {
            mongoose_cpp::Manager manager;
            auto conn = manager.connect("invalid://url", 
                [](mongoose_cpp::Connection& conn, int event, void* event_data) {
                    (void)conn; (void)event; (void)event_data;
                });
            std::cout << "  ✗ Should have thrown exception" << std::endl;
        } catch (const mongoose_cpp::NetworkException& e) {
            std::cout << "  ✓ Caught NetworkException: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            std::cout << "  ✓ Caught exception: " << e.what() << std::endl;
        }
        
        std::cout << std::endl;
        
        // Unwrapped version - Return code error handling
        std::cout << "Unwrapped version (Return codes):" << std::endl;
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        
        struct mg_connection *conn = mg_connect(&mgr, "invalid://url", 
            [](struct mg_connection *c, int ev, void *ev_data) {
                (void)c; (void)ev; (void)ev_data;
            }, NULL);
        
        if (conn == NULL) {
            std::cout << "  ✓ Connection failed (returned NULL)" << std::endl;
        } else {
            std::cout << "  ✗ Connection should have failed" << std::endl;
        }
        
        mg_mgr_free(&mgr);
        
        std::cout << std::endl;
    }
    
    void test_memory_management() {
        std::cout << "Test 5: Memory Management Comparison" << std::endl;
        std::cout << "------------------------------------" << std::endl;
        
        std::cout << "Wrapped version:" << std::endl;
        std::cout << "  ✓ RAII ensures automatic cleanup" << std::endl;
        std::cout << "  ✓ shared_ptr manages connection lifetime" << std::endl;
        std::cout << "  ✓ Exception safety guaranteed" << std::endl;
        std::cout << "  ✓ No manual memory management required" << std::endl;
        
        std::cout << std::endl;
        
        std::cout << "Unwrapped version:" << std::endl;
        std::cout << "  ⚠ Manual mg_mgr_init/mg_mgr_free required" << std::endl;
        std::cout << "  ⚠ Connection cleanup handled by mg_mgr_free" << std::endl;
        std::cout << "  ⚠ Risk of memory leaks if cleanup forgotten" << std::endl;
        std::cout << "  ⚠ No exception safety guarantees" << std::endl;
        
        std::cout << std::endl;
    }
};

int main() {
    MongooseComparisonTest test;
    test.run_all_tests();
    return 0;
}
