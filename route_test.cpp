#include "mongoose_cpp.hpp"
#include <iostream>

using namespace mongoose_cpp;
using namespace mongoose_cpp::http;

int main() {
    std::cout << "Testing route matching..." << std::endl;
    
    try {
        // Test route creation and matching
        Route route(Method::GET, "/", [](const Message& req, Response& res) {
            (void)req; (void)res;
            std::cout << "Route handler called" << std::endl;
        });
        
        std::cout << "Route created with pattern: " << route.get_pattern() << std::endl;
        std::cout << "Route method: " << method_to_string(route.get_method()) << std::endl;
        
        // Test matching
        bool matches1 = route.matches(Method::GET, "/");
        bool matches2 = route.matches(Method::GET, "/test");
        bool matches3 = route.matches(Method::POST, "/");
        
        std::cout << "Route matches GET /: " << (matches1 ? "YES" : "NO") << std::endl;
        std::cout << "Route matches GET /test: " << (matches2 ? "YES" : "NO") << std::endl;
        std::cout << "Route matches POST /: " << (matches3 ? "YES" : "NO") << std::endl;
        
        // Test method conversion
        std::cout << "Method GET: " << method_to_string(Method::GET) << std::endl;
        std::cout << "String to method GET: " << method_to_string(string_to_method("GET")) << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
