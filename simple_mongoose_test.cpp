#include "mongoose_cpp.hpp"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>

extern "C" {
#include "mongoose.h"
}

class SimpleMongooseTest {
public:
    void run_tests() {
        std::cout << "=== Simple Mongoose Wrapper vs Raw API Test ===" << std::endl;
        std::cout << std::endl;
        
        test_basic_usage();
        test_http_functionality();
        test_ease_of_use();
        
        std::cout << "=== Test Summary ===" << std::endl;
        print_comparison_summary();
    }
    
private:
    void test_basic_usage() {
        std::cout << "1. Basic Usage Comparison" << std::endl;
        std::cout << "========================" << std::endl;
        
        std::cout << "\nWrapped Version (mongoose_cpp):" << std::endl;
        std::cout << "```cpp" << std::endl;
        std::cout << "mongoose_cpp::Manager manager;  // RAII - automatic init/cleanup" << std::endl;
        std::cout << "auto conn = manager.connect(\"http://example.com\", handler);" << std::endl;
        std::cout << "// Automatic cleanup when manager goes out of scope" << std::endl;
        std::cout << "```" << std::endl;
        
        std::cout << "\nUnwrapped Version (raw mongoose):" << std::endl;
        std::cout << "```c" << std::endl;
        std::cout << "struct mg_mgr mgr;" << std::endl;
        std::cout << "mg_mgr_init(&mgr);  // Manual initialization" << std::endl;
        std::cout << "struct mg_connection *conn = mg_connect(&mgr, \"http://example.com\", handler, NULL);" << std::endl;
        std::cout << "// ... use connection ..." << std::endl;
        std::cout << "mg_mgr_free(&mgr);  // Manual cleanup - MUST NOT FORGET!" << std::endl;
        std::cout << "```" << std::endl;
        
        // Demonstrate actual usage
        demonstrate_wrapped_usage();
        demonstrate_unwrapped_usage();
        
        std::cout << std::endl;
    }
    
    void demonstrate_wrapped_usage() {
        std::cout << "\nDemonstrating Wrapped Usage:" << std::endl;
        try {
            mongoose_cpp::Manager manager;
            std::cout << "  ✓ Manager created (automatic initialization)" << std::endl;
            
            // Set some configuration
            manager.set_dns_timeout(5000);
            std::cout << "  ✓ DNS timeout set to 5000ms" << std::endl;
            
            // The manager will be automatically cleaned up
            std::cout << "  ✓ Manager will be automatically cleaned up" << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "  ✗ Exception: " << e.what() << std::endl;
        }
    }
    
    void demonstrate_unwrapped_usage() {
        std::cout << "\nDemonstrating Unwrapped Usage:" << std::endl;
        
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);
        std::cout << "  ✓ Manager manually initialized" << std::endl;
        
        // Set some configuration
        mgr.dnstimeout = 5000;
        std::cout << "  ✓ DNS timeout set to 5000ms" << std::endl;
        
        // Manual cleanup
        mg_mgr_free(&mgr);
        std::cout << "  ✓ Manager manually cleaned up" << std::endl;
    }
    
    void test_http_functionality() {
        std::cout << "\n2. HTTP Functionality Comparison" << std::endl;
        std::cout << "================================" << std::endl;
        
        std::cout << "\nWrapped Version - HTTP Server:" << std::endl;
        std::cout << "```cpp" << std::endl;
        std::cout << "mongoose_cpp::Manager manager;" << std::endl;
        std::cout << "mongoose_cpp::http::Server server(manager);" << std::endl;
        std::cout << "" << std::endl;
        std::cout << "server.get(\"/\", [](const auto& req, auto& res) {" << std::endl;
        std::cout << "    res.text(\"Hello World!\").send();" << std::endl;
        std::cout << "});" << std::endl;
        std::cout << "" << std::endl;
        std::cout << "server.listen(\"http://0.0.0.0:8080\");" << std::endl;
        std::cout << "manager.start_polling_thread();" << std::endl;
        std::cout << "```" << std::endl;
        
        std::cout << "\nUnwrapped Version - HTTP Server:" << std::endl;
        std::cout << "```c" << std::endl;
        std::cout << "static void event_handler(struct mg_connection *c, int ev, void *ev_data) {" << std::endl;
        std::cout << "    if (ev == MG_EV_HTTP_MSG) {" << std::endl;
        std::cout << "        struct mg_http_message *hm = (struct mg_http_message *) ev_data;" << std::endl;
        std::cout << "        if (mg_match(hm->uri, mg_str(\"/\"), NULL)) {" << std::endl;
        std::cout << "            mg_http_reply(c, 200, \"Content-Type: text/plain\\r\\n\", \"Hello World!\");" << std::endl;
        std::cout << "        }" << std::endl;
        std::cout << "    }" << std::endl;
        std::cout << "}" << std::endl;
        std::cout << "" << std::endl;
        std::cout << "struct mg_mgr mgr;" << std::endl;
        std::cout << "mg_mgr_init(&mgr);" << std::endl;
        std::cout << "mg_listen(&mgr, \"http://0.0.0.0:8080\", event_handler, NULL);" << std::endl;
        std::cout << "for (;;) mg_mgr_poll(&mgr, 1000);" << std::endl;
        std::cout << "```" << std::endl;
        
        // Demonstrate HTTP message handling
        demonstrate_http_message_handling();
        
        std::cout << std::endl;
    }
    
    void demonstrate_http_message_handling() {
        std::cout << "\nHTTP Message Handling Comparison:" << std::endl;
        
        std::cout << "\nWrapped Version:" << std::endl;
        std::cout << "  ✓ Type-safe HTTP method enums (GET, POST, etc.)" << std::endl;
        std::cout << "  ✓ Automatic header parsing into std::map" << std::endl;
        std::cout << "  ✓ Built-in query parameter extraction" << std::endl;
        std::cout << "  ✓ Response builder with method chaining" << std::endl;
        std::cout << "  ✓ Automatic content-type handling" << std::endl;
        
        std::cout << "\nUnwrapped Version:" << std::endl;
        std::cout << "  ⚠ Manual string comparison for HTTP methods" << std::endl;
        std::cout << "  ⚠ Manual header parsing required" << std::endl;
        std::cout << "  ⚠ Manual query parameter extraction" << std::endl;
        std::cout << "  ⚠ Manual response formatting" << std::endl;
        std::cout << "  ⚠ Manual content-type header management" << std::endl;
    }
    
    void test_ease_of_use() {
        std::cout << "\n3. Ease of Use Comparison" << std::endl;
        std::cout << "=========================" << std::endl;
        
        std::cout << "\nCode Safety:" << std::endl;
        std::cout << "Wrapped:   ✓ RAII ensures cleanup" << std::endl;
        std::cout << "           ✓ Exception safety" << std::endl;
        std::cout << "           ✓ Type safety with enums and classes" << std::endl;
        std::cout << "           ✓ shared_ptr for connection management" << std::endl;
        std::cout << "Unwrapped: ⚠ Manual resource management" << std::endl;
        std::cout << "           ⚠ Risk of memory leaks" << std::endl;
        std::cout << "           ⚠ C-style error handling" << std::endl;
        std::cout << "           ⚠ Raw pointers" << std::endl;
        
        std::cout << "\nDeveloper Experience:" << std::endl;
        std::cout << "Wrapped:   ✓ Modern C++ idioms" << std::endl;
        std::cout << "           ✓ Method chaining" << std::endl;
        std::cout << "           ✓ Lambda support" << std::endl;
        std::cout << "           ✓ STL container integration" << std::endl;
        std::cout << "Unwrapped: ⚠ C-style programming" << std::endl;
        std::cout << "           ⚠ Function pointers" << std::endl;
        std::cout << "           ⚠ Manual string handling" << std::endl;
        std::cout << "           ⚠ Verbose setup code" << std::endl;
        
        std::cout << "\nPerformance:" << std::endl;
        std::cout << "Wrapped:   ⚠ Small overhead from abstraction" << std::endl;
        std::cout << "           ✓ Optimized for common use cases" << std::endl;
        std::cout << "           ✓ Efficient memory management" << std::endl;
        std::cout << "Unwrapped: ✓ Direct C API calls" << std::endl;
        std::cout << "           ✓ Minimal overhead" << std::endl;
        std::cout << "           ⚠ Manual optimization required" << std::endl;
        
        std::cout << std::endl;
    }
    
    void print_comparison_summary() {
        std::cout << "\n📊 COMPARISON SUMMARY" << std::endl;
        std::cout << "=====================" << std::endl;
        
        std::cout << "\n🎯 Use Wrapped Version (mongoose_cpp) when:" << std::endl;
        std::cout << "  • Building modern C++ applications" << std::endl;
        std::cout << "  • Want type safety and RAII benefits" << std::endl;
        std::cout << "  • Need rapid development" << std::endl;
        std::cout << "  • Prefer exception-based error handling" << std::endl;
        std::cout << "  • Want built-in HTTP/WebSocket/MQTT helpers" << std::endl;
        
        std::cout << "\n⚡ Use Unwrapped Version (raw mongoose) when:" << std::endl;
        std::cout << "  • Maximum performance is critical" << std::endl;
        std::cout << "  • Working in C or mixed C/C++ environment" << std::endl;
        std::cout << "  • Need fine-grained control over resources" << std::endl;
        std::cout << "  • Minimal binary size is important" << std::endl;
        std::cout << "  • Integrating with existing C codebases" << std::endl;
        
        std::cout << "\n💡 Key Takeaways:" << std::endl;
        std::cout << "  • Wrapped version provides safety and convenience" << std::endl;
        std::cout << "  • Unwrapped version offers maximum control and performance" << std::endl;
        std::cout << "  • Both use the same underlying mongoose library" << std::endl;
        std::cout << "  • Choice depends on project requirements and team preferences" << std::endl;
        
        std::cout << std::endl;
    }
};

int main() {
    SimpleMongooseTest test;
    test.run_tests();
    return 0;
}
