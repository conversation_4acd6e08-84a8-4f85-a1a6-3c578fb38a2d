#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <stdexcept>
#include <sstream>
#include <initializer_list>

// Include tinyxml2
#include "xml/tinyxml2.h"

namespace xml_cpp {

// Forward declarations
class Document;
class Element;
class Attribute;
class Text;

// Exception classes
class XmlException : public std::runtime_error {
public:
    explicit XmlException(const std::string& message) 
        : std::runtime_error("XML Error: " + message) {}
};

class ParseException : public XmlException {
public:
    explicit ParseException(const std::string& message, int line = -1) 
        : XmlException("Parse Error" + (line > 0 ? " (line " + std::to_string(line) + ")" : "") + ": " + message) {}
};

class ValidationException : public XmlException {
public:
    explicit ValidationException(const std::string& message) 
        : XmlException("Validation Error: " + message) {}
};

// Utility classes
class XPath {
private:
    std::string path_;
    
public:
    explicit XPath(const std::string& path) : path_(path) {}
    const std::string& path() const { return path_; }
    
    // XPath builder methods
    static XPath root() { return XPath("/"); }
    static XPath element(const std::string& name) { return XPath("//" + name); }
    XPath child(const std::string& name) const { return XPath(path_ + "/" + name); }
    XPath attribute(const std::string& name) const { return XPath(path_ + "/@" + name); }
    XPath index(int idx) const { return XPath(path_ + "[" + std::to_string(idx + 1) + "]"); }
};

// Namespace management
class Namespace {
public:
    std::string prefix;
    std::string uri;
    
    Namespace() = default;
    Namespace(const std::string& prefix, const std::string& uri) : prefix(prefix), uri(uri) {}
    
    std::string qualified_name(const std::string& local_name) const {
        return prefix.empty() ? local_name : prefix + ":" + local_name;
    }
};

using NamespaceMap = std::map<std::string, std::string>; // prefix -> uri

// Attribute wrapper
class Attribute {
private:
    tinyxml2::XMLAttribute* attr_;
    
public:
    explicit Attribute(tinyxml2::XMLAttribute* attr) : attr_(attr) {}
    
    bool is_valid() const { return attr_ != nullptr; }
    std::string name() const { return attr_ ? attr_->Name() : ""; }
    std::string value() const { return attr_ ? attr_->Value() : ""; }
    
    // Type-safe value getters
    int int_value(int default_val = 0) const;
    double double_value(double default_val = 0.0) const;
    bool bool_value(bool default_val = false) const;
    
    // Value setters
    void set_value(const std::string& value);
    void set_value(int value);
    void set_value(double value);
    void set_value(bool value);
    
    // Internal access
    tinyxml2::XMLAttribute* get_tinyxml_attribute() { return attr_; }
    const tinyxml2::XMLAttribute* get_tinyxml_attribute() const { return attr_; }
};

// Text wrapper
class Text {
private:
    tinyxml2::XMLText* text_;
    
public:
    explicit Text(tinyxml2::XMLText* text) : text_(text) {}
    
    bool is_valid() const { return text_ != nullptr; }
    std::string value() const { return text_ ? text_->Value() : ""; }
    void set_value(const std::string& value);
    
    // Internal access
    tinyxml2::XMLText* get_tinyxml_text() { return text_; }
    const tinyxml2::XMLText* get_tinyxml_text() const { return text_; }
};

// Element wrapper
class Element {
private:
    tinyxml2::XMLElement* elem_;
    std::shared_ptr<Document> doc_; // Keep document alive
    
public:
    Element(tinyxml2::XMLElement* elem, std::shared_ptr<Document> doc);
    
    bool is_valid() const { return elem_ != nullptr; }
    std::string name() const { return elem_ ? elem_->Name() : ""; }
    std::string text() const { return elem_ ? (elem_->GetText() ? elem_->GetText() : "") : ""; }
    
    // Attribute access
    bool has_attribute(const std::string& name) const;
    Attribute get_attribute(const std::string& name) const;
    std::string get_attribute_value(const std::string& name, const std::string& default_val = "") const;
    Element& set_attribute(const std::string& name, const std::string& value);
    Element& set_attribute(const std::string& name, int value);
    Element& set_attribute(const std::string& name, double value);
    Element& set_attribute(const std::string& name, bool value);
    Element& remove_attribute(const std::string& name);
    
    // Text content
    Element& set_text(const std::string& text);
    Element& append_text(const std::string& text);
    
    // Child element access
    Element first_child(const std::string& name = "") const;
    Element next_sibling(const std::string& name = "") const;
    Element parent() const;
    std::vector<Element> children(const std::string& name = "") const;
    
    // Child element creation
    Element append_child(const std::string& name);
    Element prepend_child(const std::string& name);
    Element insert_child_after(const Element& after, const std::string& name);
    Element insert_child_before(const Element& before, const std::string& name);
    
    // Element removal
    void remove_child(const Element& child);
    void remove_children(const std::string& name = "");
    void remove_from_parent();
    
    // XPath-like queries (simplified)
    Element find(const std::string& path) const;
    std::vector<Element> find_all(const std::string& path) const;
    
    // Namespace support
    Element& set_namespace(const Namespace& ns);
    std::string get_namespace_uri() const;
    std::string get_namespace_prefix() const;
    
    // Utility methods
    bool empty() const { return !is_valid() || (!elem_->FirstChild() && !elem_->GetText()); }
    size_t child_count() const;
    
    // Internal access
    tinyxml2::XMLElement* get_tinyxml_element() { return elem_; }
    const tinyxml2::XMLElement* get_tinyxml_element() const { return elem_; }
    std::shared_ptr<Document> get_document() const { return doc_; }
};

// Document wrapper
class Document {
private:
    std::unique_ptr<tinyxml2::XMLDocument> doc_;
    NamespaceMap namespaces_;
    
public:
    Document();
    explicit Document(const std::string& xml_content);
    ~Document() = default;
    
    // Move semantics
    Document(Document&& other) noexcept;
    Document& operator=(Document&& other) noexcept;
    
    // Copy is disabled to avoid issues with tinyxml2
    Document(const Document&) = delete;
    Document& operator=(const Document&) = delete;
    
    // Parsing
    void parse(const std::string& xml_content);
    void load_file(const std::string& filename);
    
    // Serialization
    std::string to_string(bool compact = false) const;
    void save_file(const std::string& filename, bool compact = false) const;
    
    // Root element access
    Element root() const;
    Element create_root(const std::string& name);
    
    // Element creation (unlinked)
    Element create_element(const std::string& name);
    
    // Namespace management
    void add_namespace(const std::string& prefix, const std::string& uri);
    void remove_namespace(const std::string& prefix);
    const NamespaceMap& namespaces() const { return namespaces_; }
    std::string resolve_namespace(const std::string& prefix) const;
    
    // XPath-like queries
    Element find(const std::string& path) const;
    std::vector<Element> find_all(const std::string& path) const;
    
    // Validation
    bool is_valid() const;
    std::string get_error_message() const;
    int get_error_line() const;
    
    // Internal access
    tinyxml2::XMLDocument* get_tinyxml_document() { return doc_.get(); }
    const tinyxml2::XMLDocument* get_tinyxml_document() const { return doc_.get(); }
    
    // Factory method for shared ownership
    static std::shared_ptr<Document> create();
    static std::shared_ptr<Document> create(const std::string& xml_content);
    static std::shared_ptr<Document> load(const std::string& filename);
};

// Builder pattern for XML construction
class ElementBuilder {
private:
    Element element_;
    
public:
    explicit ElementBuilder(Element element) : element_(std::move(element)) {}
    
    ElementBuilder& attr(const std::string& name, const std::string& value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, int value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, double value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& attr(const std::string& name, bool value) {
        element_.set_attribute(name, value);
        return *this;
    }
    
    ElementBuilder& text(const std::string& text) {
        element_.set_text(text);
        return *this;
    }
    
    ElementBuilder child(const std::string& name, std::function<void(ElementBuilder&)> builder = nullptr) {
        Element child_elem = element_.append_child(name);
        if (builder) {
            ElementBuilder child_builder(child_elem);
            builder(child_builder);
        }
        return *this;
    }
    
    Element build() { return element_; }
    operator Element() { return element_; }
};

// Convenience functions
ElementBuilder build_element(Document& doc, const std::string& name);
ElementBuilder build_element(Element& parent, const std::string& name);

} // namespace xml_cpp
